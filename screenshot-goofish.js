const { chromium } = require('playwright');
const path = require('path');
const fs = require('fs');

async function screenshotGoofish() {
    let browser;
    
    try {
        console.log('启动浏览器...');
        browser = await chromium.launch({
            headless: false, // 设置为 true 可以无头模式运行
            slowMo: 1000    // 减慢操作速度，便于观察
        });
        
        const context = await browser.newContext({
            viewport: { width: 1920, height: 1080 }
        });
        
        const page = await context.newPage();
        
        console.log('正在访问 goofish.pro...');
        await page.goto('https://goofish.pro', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        // 等待页面完全加载
        await page.waitForTimeout(3000);
        
        // 创建screenshots目录（如果不存在）
        const screenshotDir = path.join(__dirname, 'screenshots');
        if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
        }
        
        // 生成文件名（包含时间戳）
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `goofish-pro-${timestamp}.png`;
        const filepath = path.join(screenshotDir, filename);
        
        console.log('正在截图...');
        await page.screenshot({
            path: filepath,
            fullPage: true,
            type: 'png'
        });
        
        console.log(`截图已保存到: ${filepath}`);
        
        // 获取页面标题和URL
        const title = await page.title();
        const url = page.url();
        
        console.log(`页面标题: ${title}`);
        console.log(`页面URL: ${url}`);
        
    } catch (error) {
        console.error('发生错误:', error.message);
    } finally {
        if (browser) {
            await browser.close();
            console.log('浏览器已关闭');
        }
    }
}

// 运行脚本
if (require.main === module) {
    screenshotGoofish().catch(console.error);
}

module.exports = { screenshotGoofish };
